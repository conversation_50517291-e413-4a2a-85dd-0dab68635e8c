{"format": 1, "restore": {"D:\\Development\\Unity\\UnityProjects\\Packages\\RTLTMPro-Tests.csproj": {}}, "projects": {"D:\\Development\\Unity\\UnityProjects\\Packages\\RTLTMPro-Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Development\\Unity\\UnityProjects\\Packages\\RTLTMPro-Tests.csproj", "projectName": "RTLTMPro-Tests", "projectPath": "D:\\Development\\Unity\\UnityProjects\\Packages\\RTLTMPro-Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Development\\Unity\\UnityProjects\\Packages\\Temp\\obj\\RTLTMPro-Tests\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Development\\Unity\\UnityProjects\\Packages\\RTLTMPro.csproj": {"projectPath": "D:\\Development\\Unity\\UnityProjects\\Packages\\RTLTMPro.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.205\\RuntimeIdentifierGraph.json"}}}, "D:\\Development\\Unity\\UnityProjects\\Packages\\RTLTMPro.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Development\\Unity\\UnityProjects\\Packages\\RTLTMPro.csproj", "projectName": "RTLTMPro", "projectPath": "D:\\Development\\Unity\\UnityProjects\\Packages\\RTLTMPro.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Development\\Unity\\UnityProjects\\Packages\\Temp\\obj\\RTLTMPro\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.205\\RuntimeIdentifierGraph.json"}}}}}