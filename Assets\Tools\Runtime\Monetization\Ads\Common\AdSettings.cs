using System;
using UnityEngine;

[CreateAssetMenu(fileName = "AdSettings", menuName = "Scriptable Objects/Ad System/Ad Settings")]
public class AdSettings : ScriptableObject
{
    public AdProviderSettings[] providerSettings;
    public float loadTimeout;
    public float showCooldown;
}

[Serializable]
public class AdProviderSettings
{
    public string bannerId;
    public string interstitialId;
    public string rewardedId;
    public int priority;
}
