using System.IO;
using System.Text;
using UnityEngine;
using UnityEngine.Networking;

namespace SmartVertex.Tools
{
    /// <summary>
    /// <PERSON>les sending text-to-speech requests to the OpenAI compatible API and retrieving audio data.
    /// </summary>
    public class OpenAITextToSpeech
    {
        private TTSRequest requestPayload;
        private string apiKey;
        private string apiUrl;

        /// <summary>
        /// Gets or sets the TTS request payload.
        /// </summary>
        public TTSRequest RequestPayload
        {
            get => requestPayload;
            set => requestPayload = value;
        }

        /// <summary>
        /// Gets or sets the API key.
        /// </summary>
        public string ApiKey
        {
            get => apiKey;
            set => apiKey = value;
        }

        /// <summary>
        /// Gets or sets the API URL.
        /// </summary>
        public string ApiUrl
        {
            get => apiUrl;
            set => apiUrl = value;
        }

        /// <summary>
        /// Data container for TTS request payload.
        /// </summary>
        [System.Serializable]
        public class TTSRequest
        {
            public string model;
            public string input;
            public string voice;
            public string instructions;
            public string response_format;
            public float speed;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="OpenAITextToSpeech"/> class without parameters.
        /// </summary>
        public OpenAITextToSpeech() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="OpenAITextToSpeech"/> class with a request payload.
        /// </summary>
        public OpenAITextToSpeech(TTSRequest requestPayload)
        {
            this.requestPayload = requestPayload;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="OpenAITextToSpeech"/> class with a request payload and API key.
        /// </summary>
        public OpenAITextToSpeech(TTSRequest requestPayload, string apiKey)
        {
            this.requestPayload = requestPayload;
            this.apiKey = apiKey;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="OpenAITextToSpeech"/> class with a request payload, API key, and API URL.
        /// </summary>
        public OpenAITextToSpeech(TTSRequest requestPayload, string apiKey, string apiUrl)
        {
            this.requestPayload = requestPayload;
            this.apiKey = apiKey;
            this.apiUrl = apiUrl;
        }

        /// <summary>
        /// Generates an AudioClip from the TTS request asynchronously.
        /// </summary>
        /// <returns>The generated AudioClip, or null on failure.</returns>
        public async Awaitable<AudioClip> Generate()
        {
            byte[] ttsBytes = await SendTTSRequestAsync();
            return await CreateAudioClipFromMp3Async(ttsBytes);
        }

        private async Awaitable<byte[]> SendTTSRequestAsync()
        {
            var ttsRequest = new TTSRequest
            {
                model = requestPayload.model,
                input = requestPayload.input,
                voice = requestPayload.voice,
                instructions = requestPayload.instructions,
                response_format = "mp3",
                speed = requestPayload.speed
            };

            string jsonPayload = JsonUtility.ToJson(ttsRequest);
            byte[] bodyRaw = Encoding.UTF8.GetBytes(jsonPayload);

            using (var request = new UnityWebRequest(apiUrl, "POST"))
            {
                request.uploadHandler = new UploadHandlerRaw(bodyRaw);
                request.downloadHandler = new DownloadHandlerBuffer();
                request.SetRequestHeader("Content-Type", "application/json");
                request.SetRequestHeader("Authorization", $"Bearer {apiKey}");

                await request.SendWebRequest();

                if (request.result == UnityWebRequest.Result.Success)
                {
                    return request.downloadHandler.data;
                }
                else
                {
                    Debug.LogError($"API request failed: {request.error}");
                    Debug.LogError($"Response: {request.downloadHandler.text}");
                    return null;
                }
            }
        }

        private async Awaitable<AudioClip> CreateAudioClipFromMp3Async(byte[] audioData)
        {
            string tempPath = Path.Combine(Application.persistentDataPath, "temp_audio.mp3");
            File.WriteAllBytes(tempPath, audioData);

            using (var www = UnityWebRequestMultimedia.GetAudioClip($"file://{tempPath}", AudioType.MPEG))
            {
                await www.SendWebRequest();

                if (www.result == UnityWebRequest.Result.Success)
                {
                    AudioClip clip = DownloadHandlerAudioClip.GetContent(www);
                    try
                    {
                        File.Delete(tempPath);
                    }
                    catch (IOException ex)
                    {
                        Debug.LogWarning($"Failed to delete temp audio file: {ex.Message}");
                    }
                    return clip;
                }
                else
                {
                    Debug.LogError($"Failed to load audio clip: {www.error}");
                    try
                    {
                        File.Delete(tempPath);
                    }
                    catch (IOException ex)
                    {
                        Debug.LogWarning($"Failed to delete temp audio file: {ex.Message}");
                    }
                    return null;
                }
            }
        }
    }
}