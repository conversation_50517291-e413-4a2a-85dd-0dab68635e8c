namespace SmartVertex.Tools
{
    /// <summary>
    /// Represents the different types of advertisements that can be displayed.
    /// </summary>
    public enum AdType
    {
        /// <summary>
        /// Banner advertisement displayed at the top or bottom of the screen.
        /// </summary>
        Banner = 0,

        /// <summary>
        /// Full-screen advertisement that appears between content transitions.
        /// </summary>
        Interstitial = 1,

        /// <summary>
        /// Advertisement that provides rewards to users upon completion.
        /// </summary>
        Rewarded = 2
    }

    /// <summary>
    /// Represents the current state of an advertisement unit.
    /// </summary>
    public enum AdState
    {
        /// <summary>
        /// The ad unit is idle and ready to load.
        /// </summary>
        Idle = 0,

        /// <summary>
        /// The ad unit is currently loading content.
        /// </summary>
        Loading = 1,

        /// <summary>
        /// The ad unit has successfully loaded and is ready to show.
        /// </summary>
        Loaded = 2,

        /// <summary>
        /// The ad unit is currently being displayed to the user.
        /// </summary>
        Showing = 3,

        /// <summary>
        /// The ad unit is in cooldown period before it can be shown again.
        /// </summary>
        Cooldown = 4,

        /// <summary>
        /// The ad unit has failed to load or show.
        /// </summary>
        Failed = 5
    }

    /// <summary>
    /// Represents the result of an advertisement operation.
    /// </summary>
    public enum AdOperationResult
    {
        /// <summary>
        /// The operation completed successfully.
        /// </summary>
        Succeeded = 0,

        /// <summary>
        /// The operation failed to complete.
        /// </summary>
        Failed = 1,

        /// <summary>
        /// The operation timed out before completion.
        /// </summary>
        Timeout = 2,

        /// <summary>
        /// The operation was cancelled before completion.
        /// </summary>
        Cancelled = 3
    }
}