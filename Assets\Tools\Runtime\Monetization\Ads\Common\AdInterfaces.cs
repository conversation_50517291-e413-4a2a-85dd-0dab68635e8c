using System;
using System.Threading;
using UnityEngine;
using UnityEngine.Events;

namespace SmartVertex.Tools
{
    /// <summary>
    /// Defines the contract for an advertisement unit that can be loaded and displayed.
    /// </summary>
    public interface IAdUnit
    {
        /// <summary>
        /// Gets the type of advertisement this unit represents.
        /// </summary>
        AdType Type { get; }

        /// <summary>
        /// Gets the unique identifier for this ad unit.
        /// </summary>
        string UnitId { get; }

        /// <summary>
        /// Gets the current state of this ad unit.
        /// </summary>
        AdState State { get; }

        /// <summary>
        /// Asynchronously loads the advertisement content with a specified timeout.
        /// </summary>
        /// <param name="timeout">The maximum time to wait for the ad to load.</param>
        /// <param name="ct">Cancellation token to cancel the loading operation.</param>
        /// <returns>The result of the load operation.</returns>
        Awaitable<AdOperationResult> LoadAsync(TimeSpan timeout, CancellationToken ct);

        /// <summary>
        /// Asynchronously shows the loaded advertisement.
        /// </summary>
        /// <param name="ct">Cancellation token to cancel the show operation.</param>
        /// <returns>The result of the show operation.</returns>
        Awaitable<AdOperationResult> ShowAsync(CancellationToken ct);

        /// <summary>
        /// Event raised when the advertisement is clicked by the user.
        /// </summary>
        event UnityAction OnClicked;

        /// <summary>
        /// Event raised when the advertisement impression is recorded.
        /// </summary>
        event UnityAction OnImpression;

        /// <summary>
        /// Event raised when the advertisement generates revenue.
        /// The first parameter is the currency code, the second parameter is the revenue value.
        /// </summary>
        event UnityAction<string, double> OnPaid;
    }

    public interface IRewardedAd : IAdUnit
    {
        event UnityAction OnRewardEarned;
    }

    public interface IInterstitialAd : IAdUnit { }

    public interface IBannerAd : IAdUnit
    {
        void Hide();
        void Destroy();
    }
}